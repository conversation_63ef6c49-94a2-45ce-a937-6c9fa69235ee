export default function ProductsSection() {
  return (
    <section id="products" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <div className="inline-block px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full font-medium text-sm mb-4">
            Our Products
          </div>
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            Innovative Solutions for the Digital Economy
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our cutting-edge products that are shaping the future of business and technology.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Product 1: AdukromKingdom */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden card-hover animate-fade-in-scale">
            <div className="h-64 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-indigo-600 opacity-90"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="blob w-40 h-40 bg-white/20 animate-float"></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-8">
                <h3 className="text-2xl font-bold text-white">AdukromKingdom</h3>
                <p className="text-white/80">Digital Innovation Platform</p>
              </div>
            </div>
            <div className="p-8">
              <p className="text-gray-600 mb-6">
                A comprehensive digital platform that brings together innovative technologies and business solutions to create new opportunities in the digital economy.
              </p>

              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-purple-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Digital Platform</h4>
                  <p className="text-sm text-gray-600">Comprehensive ecosystem</p>
                </div>

                <div className="bg-purple-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Innovation Hub</h4>
                  <p className="text-sm text-gray-600">Technology integration</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-1 rounded-full">Platform</span>
                <span className="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-1 rounded-full">Digital</span>
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full">Innovation</span>
              </div>

              <a href="#" className="inline-flex items-center text-purple-600 font-medium hover:text-purple-800 transition-colors">
                Explore AdukromKingdom
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>

          {/* Product 2: Lightace Global */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden card-hover animate-fade-in-scale" style={{animationDelay: '0.2s'}}>
            <div className="h-64 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-cyan-600 opacity-90"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="blob w-40 h-40 bg-white/20 animate-float" style={{animationDelay: '2s'}}></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-8">
                <h3 className="text-2xl font-bold text-white">Lightace Global</h3>
                <p className="text-white/80">Global Business Solutions</p>
              </div>
            </div>
            <div className="p-8">
              <p className="text-gray-600 mb-6">
                International business development and consulting services that help companies expand globally and navigate complex international markets.
              </p>

              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Global Reach</h4>
                  <p className="text-sm text-gray-600">International expansion</p>
                </div>

                <div className="bg-blue-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Expert Consulting</h4>
                  <p className="text-sm text-gray-600">Strategic guidance</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full">Global</span>
                <span className="bg-cyan-100 text-cyan-800 text-xs font-medium px-2.5 py-1 rounded-full">Consulting</span>
                <span className="bg-teal-100 text-teal-800 text-xs font-medium px-2.5 py-1 rounded-full">Business</span>
              </div>

              <a href="#" className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors">
                Learn About Lightace
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>

          {/* Product 3: xAuBIT */}
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden card-hover animate-fade-in-scale" style={{animationDelay: '0.4s'}}>
            <div className="h-64 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-yellow-600 opacity-90"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="blob w-40 h-40 bg-white/20 animate-float" style={{animationDelay: '4s'}}></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-8">
                <h3 className="text-2xl font-bold text-white">xAuBIT</h3>
                <p className="text-white/80">Gold-Pegged Stablecoin Solution</p>
              </div>
            </div>
            <div className="p-8">
              <p className="text-gray-600 mb-6">
                xAuBIT is a stablecoin pegged to gold, offering a one-to-one value with one troy ounce of gold. Built on the Bitcoin blockchain via the Omni Layer protocol, xAuBIT combines the reliability of gold with the efficiency of cryptocurrency.
              </p>

              <div className="grid grid-cols-2 gap-4 mb-8">
                <div className="bg-yellow-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Gold-Backed</h4>
                  <p className="text-sm text-gray-600">1:1 ratio with gold reserves</p>
                </div>

                <div className="bg-yellow-50 rounded-xl p-4">
                  <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mb-3">
                    <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <h4 className="font-semibold mb-1">Bitcoin Blockchain</h4>
                  <p className="text-sm text-gray-600">Built on proven technology</p>
                </div>
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-1 rounded-full">Stablecoin</span>
                <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-1 rounded-full">Gold-Pegged</span>
                <span className="bg-amber-100 text-amber-800 text-xs font-medium px-2.5 py-1 rounded-full">Bitcoin Network</span>
                <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-1 rounded-full">Institutional</span>
              </div>

              <a href="#" className="inline-flex items-center text-yellow-600 font-medium hover:text-yellow-800 transition-colors">
                Learn About xAuBIT
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
