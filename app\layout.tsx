import type { <PERSON>ada<PERSON> } from 'next'
import { Montserrat } from 'next/font/google'
import './globals.css'
import CircuitClickEffect from './components/CircuitClickEffect'

const montserrat = Montserrat({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Boriqn - Blockchain Innovation',
  description: 'Empowering Puerto Rico and beyond through innovation, blockchain technology, and strategic business development solutions.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={montserrat.className}>
        <CircuitClickEffect />
        {children}
      </body>
    </html>
  )
}
