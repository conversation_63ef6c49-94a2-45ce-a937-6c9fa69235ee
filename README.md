# Boriqn - Innovation & Blockchain Solutions

A modern, responsive website for Boriqn, built with Next.js and featuring beautiful animations, gradients, and glass morphism effects.

## Features

- Modern React-based frontend with smooth animations
- Responsive design that works on all devices
- Interactive UI elements with micro-interactions
- Express backend for API endpoints
- Maintains the original color scheme and branding

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository
2. Run the development script:

```bash
# On Windows
start-dev.bat

# On Mac/Linux
# First make the script executable
chmod +x start-dev.sh
# Then run it
./start-dev.sh
```

Alternatively, you can run the commands manually:

```bash
# Install server dependencies
npm install

# Install frontend dependencies
cd frontend
npm install
cd ..

# Build and start the server
npm run dev
```

### Development

For frontend development with hot reloading:

```bash
npm run frontend:dev
```

This will start the Vite development server at http://localhost:3000.

For full-stack development:

```bash
npm run dev
```

This will build the frontend and start the Express server at http://localhost:5000.

## Project Structure

- `/frontend` - React frontend application
  - `/src` - React components and pages
  - `/public` - Static assets
- `/src` - Express server
- `/dist` - Compiled server code

## Technologies Used

- React
- Framer Motion for animations
- AOS (Animate on Scroll)
- Express
- TypeScript
- Vite
